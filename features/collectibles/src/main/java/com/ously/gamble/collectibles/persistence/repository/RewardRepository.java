package com.ously.gamble.collectibles.persistence.repository;

import com.ously.gamble.collectibles.persistence.model.Reward;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RewardRepository extends JpaRepository<Reward, Integer> {

    List<Reward> findByCardCollectionId(Integer cardCollectionId);

    List<Reward> findByCardId(Integer cardId);
}
