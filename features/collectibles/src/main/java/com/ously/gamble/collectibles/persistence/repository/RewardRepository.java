package com.ously.gamble.collectibles.persistence.repository;

import com.ously.gamble.collectibles.persistence.model.Reward;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RewardRepository extends JpaRepository<Reward, Integer> {

    List<Reward> findByCardCollectionId(Integer cardCollectionId);

    @Query("SELECT r FROM Reward r WHERE r.cardCollection.id IN :collectionIds")
    List<Reward> findByCardCollectionIdIn(@Param("collectionIds") List<Integer> collectionIds);

    List<Reward> findByCardId(Integer cardId);

    @Query("SELECT r FROM Reward r WHERE r.card.id IN :cardIds")
    List<Reward> findByCardIdIn(@Param("cardIds") List<Integer> cardIds);
}
